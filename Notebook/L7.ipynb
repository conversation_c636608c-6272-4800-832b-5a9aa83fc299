import requests

# 确保你的FastAPI服务器正在运行！

# 测试第一个端点: /
try:
    response_root = requests.get("http://127.0.0.1:8000/")
    print("请求 / 的结果:", response_root.json())
except requests.ConnectionError:
    print("连接服务器失败，请确保 uvicorn main:app --reload 正在运行！")

# 测试第二个端点: /greet
try:
    # 构造带参数的URL
    params = {'name': '工程师'}
    response_greet = requests.get("http://127.0.0.1:8000/greet", params=params)
    print("请求 /greet 的结果:", response_greet.json())
except requests.ConnectionError:
    print("连接服务器失败！")

import requests

# 发送POST请求，创建一条留言
message_data = {
    "name": "小明",
    "content": "这是我的第一条留言！"
}

try:
    # 使用json参数发送JSON数据
    response = requests.post("http://127.0.0.1:8000/messages", json=message_data)
    print("POST请求结果:", response.json())
    
    # 再发送一条留言
    message_data2 = {"name": "小红", "content": "我也来留言啦！"}
    requests.post("http://127.0.0.1:8000/messages", json=message_data2)
    
    # 查看所有留言
    all_messages = requests.get("http://127.0.0.1:8000/messages")
    print("所有留言:", all_messages.json())
    
except requests.ConnectionError:
    print("连接服务器失败，请确保服务器正在运行！")

import requests

# 测试查询指定同学生日
try:
    response = requests.get("http://127.0.0.1:8000/birthday/小明")
    print("小明的生日:", response.json())
    
    # 测试添加新同学生日
    new_birthday = {"name": "小王", "birthday": "2010-12-25"}
    add_response = requests.post("http://127.0.0.1:8000/birthday", json=new_birthday)
    print("添加结果:", add_response.json())
    
    # 测试查看所有生日
    all_birthdays = requests.get("http://127.0.0.1:8000/birthdays")
    print("所有生日:", all_birthdays.json())
    
except requests.ConnectionError:
    print("连接失败，请检查服务器是否运行")
except Exception as e:
    print(f"测试出错: {e}")