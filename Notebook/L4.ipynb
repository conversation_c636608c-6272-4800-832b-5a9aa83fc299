import requests
import matplotlib.pyplot as plt
from PIL import Image as PILImage # 从Pillow库导入Image，并重命名以防混淆
import io

# 这个API的URL很简单，每次访问都会返回一张新的人脸图片
url = "https://thispersondoesnotexist.com/"

# 发送GET请求
response = requests.get(url)

# 检查请求是否成功 (状态码 200)
if response.status_code == 200:
    # 关键步骤：使用 .content 获取原始的二进制数据
    # 这时，image_data 变量里存储的就是完整的图片文件内容
    image_data = response.content
    print(f"成功获取图片！图片大小: {len(image_data)} 字节")
    image_stream = io.BytesIO(image_data)
    
    # 2. 使用 Pillow (PIL) 打开这个内存文件
    img = PILImage.open(image_stream)
    
    # 3. 使用 Matplotlib 显示图片
    plt.imshow(img)
    plt.axis('off') # 关闭坐标轴，让图片更美观
    plt.show()
else:
    print(f"获取图片失败, 状态码: {response.status_code}")
    # 为了让后续代码能安全运行，即使失败也定义一下这个变量
    image_data = None 

# ... 上面是我们写好的获取图片的代码
# 确保我们之前成功获取了图片数据
if image_data:
    try:
        # 使用 'with' 语句和 'wb' (写入二进制) 模式打开（或创建）一个文件
        # 我们给它起名叫 non_existent_person.jpg
        with open("non_existent_person.jpg", "wb") as f:
            # 将之前获取的二进制数据（image_data）完整地写入这个文件
            f.write(image_data)
        print("图片已成功保存为 non_existent_person.jpg")
    except IOError as e:
        # 如果发生错误（比如磁盘满了、没有权限），打印错误信息
        print(f"文件保存失败: {e}")
else:
    print("没有可供保存的图片数据。")

# tqdm是一个非常流行的进度条库
from tqdm import tqdm

url = "https://picsum.photos/1920/1080" # 获取一个更大的随机图片
try:
    # 关键点1: 设置 stream=True，让请求以流模式进行
    response = requests.get(url, stream=True)
    response.raise_for_status() # 检查请求是否成功

    # 从响应头获取文件总大小，用于tqdm计算进度。如果获取不到，默认为0。
    total_size = int(response.headers.get('content-length', 0))

    with open("big_image.jpg", "wb") as f:
        # 关键点2: 使用tqdm包装迭代器以显示进度条
        progress_bar = tqdm(response.iter_content(chunk_size=1024), 
                          total=total_size, 
                          unit='B', # 单位是字节
                          unit_scale=True, # 自动转换单位 (KB, MB)
                          desc="Downloading big_image.jpg")
        for chunk in progress_bar:
            f.write(chunk) # 边下载边写入文件
            
    print("\n大图片下载完成！")
except requests.exceptions.RequestException as e:
    print(f"网络请求失败: {e}")
except IOError as e:
    print(f"文件保存失败: {e}")

# 这个库用于在Jupyter Notebook中直接显示图片
from IPython.display import Image, display
import os

# 定义我们要检查的图片文件名
image_filename = "IMG_0559.JPG"

# 先检查文件是否存在
if os.path.exists(image_filename):
    # 如果存在，就显示它
    display(Image(filename=image_filename))
else:
    # 如果不存在，给出友好的提示
    print(f"错误：图片文件 '{image_filename}' 未找到。")

# Pillow库，通常简写为PIL，是Python图像处理的基础库
from PIL import Image
# exifread库专门用来读取照片的EXIF元数据
import exifread
import os

# 定义一个函数，用Pillow读取最基本的信息
def print_basic_info(filename):
    try:
        with Image.open(filename) as img:
            print(f"尺寸 (宽度, 高度): {img.size}")
            print(f"格式: {img.format}")
    except Exception as e:
        print(f"Pillow读取失败: {e}")

# 定义一个函数，用exifread读取我们感兴趣的详细信息
def print_interesting_exif(filename):
    # 定义我们感兴趣的标签和它们更友好的中文名称
    interesting_tags = {
        'Image Make': '相机制造商',
        'Image Model': '相机型号',
        'EXIF LensModel': '镜头型号',
        'EXIF DateTimeOriginal': '拍摄时间',
        'EXIF ISOSpeedRatings': 'ISO速度',
        'EXIF FNumber': '光圈大小',
        'EXIF ExposureTime': '快门速度/曝光时间',
        'GPS GPSLatitude': 'GPS纬度',
        'GPS GPSLongitude': 'GPS经度'
    }
    
    try:
        # 注意：读取EXIF需要以二进制模式('rb')打开文件
        with open(filename, 'rb') as f:
            # details=False表示不读取不重要的制造商私有标签，加快速度
            tags = exifread.process_file(f, details=False)
        
        found_any = False
        for tag, name in interesting_tags.items():
            if tag in tags:
                print(f"{name}: {tags[tag]}")
                found_any = True
            else:
                print(f"{name}: 未找到")
        
    except Exception as e:
        print(f"exifread读取失败: {e}")

image_filename = "IMG_0559.JPG"
if os.path.exists(image_filename):
    print("--- 使用 Pillow 读取基本信息 ---")
    print_basic_info(image_filename)
    
    print("\n--- 使用 exifread 读取详细元数据 ---")
    print_interesting_exif(image_filename)
else:
    print(f"\n无法读取元数据，因为图片文件 '{image_filename}' 不存在。")

import exifread

# 读取EXIF数据
with open('IMG_0559.JPG', 'rb') as f:
    tags = exifread.process_file(f)

# 显示所有标签
print("部分EXIF标签：")
lines = 15
switch = 1
for tag in tags.keys():
    if lines <= 0:
        break
    if "EXIF" in tag:
        tag = tag.replace("EXIF ", "")
    if switch % 3 != 0:
        print(f"{tag}, ", end='')
    else:
        print(f"{tag}")
        lines = lines - 1
    switch = switch + 1

# 获取特定信息
camera_make = tags.get('Image Make', '未知')
camera_model = tags.get('Image Model', '未知')
date_taken = tags.get('Image DateTime', '未知')

print(f"\n相机: {camera_make} {camera_model}")
print(f"拍摄时间: {date_taken}")